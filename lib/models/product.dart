class Product {
  final int id;
  final String name;
  final String imageUrl;
  final double price;
  bool isFavorite; //สร้างขึ้นเพื่อรับค่า Favorite 

  Product({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.price,
    this.isFavorite = false,
  });

  // Factory method สำหรับสร้าง Product object จาก Map (JSON)
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as int, 
      name: json['name'] as String, 
      imageUrl: json['image_url'] as String, 
      price: (json['price'] as num).toDouble(),
      isFavorite: json['is_favorite'] ?? false, // จากข้อมูลที่กำหนดให้ไม่มีค่า is_favorite ดังนั้นจึงกำหนดค่าเริ่มต้นเป็น false
    );
  }
}