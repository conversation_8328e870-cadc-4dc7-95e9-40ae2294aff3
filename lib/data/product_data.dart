import 'dart:convert';
import '../models/product.dart';

class ProductDataSource {
  static final ProductDataSource _instance = ProductDataSource._internal();

  // กำหนดให้การสร้างตัวแปร _instance เกิดขึ้นเพียงครั้งเดียว ตลอดการใช้งาน
  // เพื่อให้เวลาเรียกใช้ ค่า _instance Reset เวลาเรียกใช้งาน
  factory ProductDataSource() {
    return _instance;
  }

  ProductDataSource._internal() {
    _loadInistialProduct();
  }

  late List<Product> _allProduct;

  // สิ้นค้าทั้งหมด สำหรับหน้า HOME
  List<Product> get allProduct => _allProduct;

  // สินค้าที่ save ไว้สำหรับหน้า save
  List<Product> get favoriteProducts =>
      _allProduct.where((p) => p.isFavorite).toList(); // เงื่อนไขคือ สถานะ isFavorite = true

  void _loadInistialProduct() {
    // ข้อมูลที่ถูกกำหนดมา
    final String _jsonData = r'''
      { 
      "product_items": [
        {
          "id": 1,
          "name": "<PERSON><PERSON> White (New)",
          "image_url":
          "https://images.unsplash.com/photo-1608231387042-66d1773070a5?fit=crop&w=300&q=80",
          "price": 250.00
        },
        {
          "id": 2,
          "name": "iPhone 12 Pro Black Edition",
          "image_url":
          "https://images.unsplash.com/photo-1573148195900-7845dcb9b127?fit=crop&w=300&q=80",
          "price": 1200.00
        },
        {
          "id": 3,
          "name": "Nintendo Switch 2021",
          "image_url":
          "https://images.unsplash.com/photo-1578303512597-81e6cc155b3e?fit=crop&w=300&q=80",
          "price": 599.00
        },
        {
          "id": 4,
          "name": "Black + Decker",
          "image_url":
          "https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?fit=crop&w=300&q=80",
          "price": 149.00
        },
        {
          "id": 5,
          "name": "White Neat Mug",
          "image_url":
          "https://images.unsplash.com/photo-1514228742587-6b1558fcca3d?fit=crop&w=300&q=80",
          "price": 35.00
        },
        {
          "id": 6,
          "name": "SMEG Oven - Winter Collection",
          "image_url":
          "https://images.unsplash.com/photo-1586208958839-06c17cacdf08?fit=crop&w=300&q=80",
          "price": 8299.00
        },
        {
          "id": 7,
          "name": "Black Table Fan with Pink Moody Cat",
          "image_url":
          "https://images.unsplash.com/photo-1618941716939-553df3c6c278?fit=crop&w=300&q=80",
          "price": 79.00
        }
      ]
      }
      ''';
    try {
      final Map<String, dynamic> decodeData = jsonDecode(_jsonData); // Decode JSON
      final List<dynamic> productItems = decodeData['product_items']; // กำหนดให้ดึงข้อมูลจาก product_items
      _allProduct = productItems.map((item) => Product.fromJson(item)).toList(); // MAP ข้อมูล
    } catch (e) {
      print("Error parsing JSON in ProductDataSource: $e");
      _allProduct = [];
    }
  }
}
