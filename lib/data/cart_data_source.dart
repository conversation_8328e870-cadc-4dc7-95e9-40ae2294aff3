import 'package:flutter/material.dart';
import '../models/product.dart';

class CartItem {
  final Product product;
  int quantity;

  CartItem({required this.product, this.quantity = 1});

  // เพิ่มจำนวนสินค้า
  void incrementQuantity() {
    quantity++;
  }

  // ลดจำนวนสินค้า
  void decrementQuantity() {
    quantity--;
  }

}

class CartDataSource extends ChangeNotifier {
  static final CartDataSource _instance = CartDataSource._internal();

  // กำหนดให้การสร้างตัวแปร _instance เกิดขึ้นเพียงครั้งเดียว ตลอดการใช้งาน 
  // เพื่อให้เวลาเรียกใช้ ค่า _instance Reset เวลาเรียกใช้งาน 
  factory CartDataSource() {
    return _instance;
  }

  CartDataSource._internal();

  final List<CartItem> _cartItems = [];

  // unmodifiable เป็นการกำหนดให้ list นี้แก้ไขโดยตรงได้ภายใต้ CartDataSource เท่านั้น
  List<CartItem> get cartItems => List.unmodifiable(_cartItems);

  // เพิ่มของตะกร้า
  void addToCart(Product product) {
    // ตรวจสอบว่ามีสินค้านี้อยู่ในตะกร้าแล้วหรือไม่
    int existingIndex = _cartItems.indexWhere(
      (item) => item.product.id == product.id,
    );
    if (existingIndex != -1) {
      // ถ้ามีแล้ว ให้เพิ่มจำนวน
      _cartItems[existingIndex].incrementQuantity();
    } else {
      // ถ้ายังไม่มี ให้เพิ่มสินค้าใหม่เข้าไป
      _cartItems.add(CartItem(product: product));
    }

    notifyListeners(); // สั่งให้ UI Update เมื่อได้รับการแจ้งเตือนจาก Widget

  }

  // เมธอดสำหรับอัปเดตจำนวนสินค้า
  void updateCartItemQuantity(int productId, int newQuantity) {
    if (newQuantity < 1) {
      removeItemFromCart(productId); // ถ้าจำนวนน้อยกว่า 1 ให้ลบออกจากตะกร้า
      return;
    }
    int index = _cartItems.indexWhere((item) => item.product.id == productId);
    if (index != -1) {
      _cartItems[index].quantity = newQuantity;
      notifyListeners(); // สั่งให้ UI Update เมื่อได้รับการแจ้งเตือนจาก Widget
    }
  }

  // เมธอดสำหรับลบสินค้าออกจากตะกร้า
  void removeItemFromCart(int productId) {
    _cartItems.removeWhere((item) => item.product.id == productId);
    notifyListeners(); // สั่งให้ UI Update เมื่อได้รับการแจ้งเตือนจาก Widget
  }

  // เมธอดสำหรับล้างตะกร้าทั้งหมด
  void clearCart() {
    _cartItems.clear();
    notifyListeners();
  }

  // เมธอดสำหรับคำนวณราคารวม
  double calculateTotalPrice() {
    return _cartItems.fold(
      0.0,
      (sum, item) => sum + (item.product.price * item.quantity),
    );
  }

}
