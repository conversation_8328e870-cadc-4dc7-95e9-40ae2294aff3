import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

// หากต้องการใช้ API ค่อยเปิด
// import 'package:http/http.dart' as http;
// import 'dart:convert';

class CheckoutPage extends StatefulWidget {
  final double totalPrice;

  const CheckoutPage({Key? key, required this.totalPrice}) : super(key: key);

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  String? _qrCodeData; // สถานะสำหรับเก็บข้อมูล QR Code ที่ได้จาก API
  bool _isLoading = true; // สถานะสำหรับแสดง Loading indicator
  String? _errorMessage; // สถานะสำหรับเก็บข้อผิดพลาด

  @override
  void initState() {
    super.initState();
    _fetchQrCode(); // เรียก API เมื่อ Widget ถูกสร้างขึ้น
  }

  // เรียก API
  Future<void> _fetchQrCode() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null; // รีเซ็ตข้อผิดพลาดก่อนเริ่ม fetch
    });

    // --- ส่วนจำลองข้อมูล QR Code ---
    // จำลองการโหลดข้อมูล 2 วินาที
    await Future.delayed(const Duration(seconds: 2));

    // กำหนดข้อมูล QR Code เป็น "Testing"
    setState(() {
      _qrCodeData = 'Testing'; // <-- ช่องใส่ข้อมูลเพื่อจำลอง QR Code
      _isLoading = false;
    });
    // --- สิ้นสุดส่วนจำลองข้อมูล QR Code ---

    /*  ส่วนร้องขอ API จาก URL
    // ส่งราคาเพื่อร้องขอข้อมูลจาก API
    final String url =
        'https://payment.spw.challenge/checkout?price=${widget.totalPrice.toStringAsFixed(2)}';
    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data.containsKey('qrcode')) {
          setState(() {
            _qrCodeData = data['qrcode'];
            _isLoading = false;
          });
        } else {
          setState(() {
            _errorMessage = 'QR Code data not found in response.';
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _errorMessage =
              'Failed to load QR Code. Status code: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Network error: $e';
        _isLoading = false;
      });
    }
    */
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const Icon(Icons.arrow_back_ios),
        ),
        title: const Text('Checkout'),
      ),

      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // QRCode
              if (_isLoading)
                const CircularProgressIndicator(), // แสดง Loading indicator
              if (_errorMessage != null)
                Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              // ถ้ามีข้อมูล QR Code แล้ว ให้แสดง QR Code
              if (!_isLoading && _errorMessage == null && _qrCodeData != null)
                QrImageView(
                  data: _qrCodeData!,
                  version: QrVersions.auto,
                  size: 250.0,
                  backgroundColor: Colors.white,
                  gapless: true, // ทำให้ QR Code ไม่มีช่องว่างระหว่างจุด

                  // หากต้องการเพิ่มรูปโลโก้ตรงกลาง
                  // embeddedImage: AssetImage('assets/your_logo.png'),
                  // embeddedImageStyle: QrEmbeddedImageStyle(
                  //   size: Size(50, 50),
                  // ),

                ),

              const SizedBox(height: 30),
              const Text(
                'Scan & Pay',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),

              // ราคา
              Text(
                '\$${widget.totalPrice.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
