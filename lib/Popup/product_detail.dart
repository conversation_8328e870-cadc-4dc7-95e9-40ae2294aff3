import 'package:flutter/material.dart';
import '../models/product.dart';
import '../data/cart_data_source.dart';

class ProductDetail extends StatefulWidget {
  final Product product;

  const ProductDetail({Key? key, required this.product}) : super(key: key);

  @override
  State<ProductDetail> createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductDetail> {
  late bool _isFavorite;
  final CartDataSource _cartDataSource = CartDataSource();

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.product.isFavorite;
  }

  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
      widget.product.isFavorite = _isFavorite; // Update the product object

      // popup Save and Unsave
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isFavorite
                ? "Save ${widget.product.name}"
                : "Unsave ${widget.product.name}",
          ),
          duration: Duration(milliseconds: 700), // ระยะเวลาแสดงข้อความ 0.7 วิ
        ),
      );
    });
  }

  // เมธอดสำหรับเพิ่มสินค้าลงตะกร้า
  void _addToCart() {
    _cartDataSource.addToCart(widget.product);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${widget.product.name} added to cart!'),
        duration: Duration(milliseconds: 1000),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // แบ่ง 3 ส่วน
      // 1. appBar ด้านบน ปุ่มย้อนกลับ
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context); // ย้อนไปหน้าก่อนหน้า
          },
          icon: Icon(Icons.arrow_back_ios),
        ),
        title: Text('Product Detail'),
        centerTitle: true,
        elevation: 0,
      ),

      // 2. body พื้นที่แสดงรายละเอียดสินค้า
      // SingleChildScrollView กันกรณีข้อมูลล้นจอจะได้เลื่อนดูได้
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // รูป
            Container(
              width: double.infinity,
              height: MediaQuery.of(context).size.height * 0.4,
              color: Colors.grey[200],
              child: Stack(
                children: [
                  Positioned.fill(
                    child: Image.network(
                      widget.product.imageUrl,
                      fit: BoxFit.cover,
                      loadingBuilder:
                          (
                            BuildContext context,
                            Widget child,
                            ImageChunkEvent? loadingProgress,
                          ) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value:
                                    loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[200],
                          child: Center(
                            child: Icon(
                              Icons.broken_image,
                              color: Colors.grey,
                              size: 50,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            // รายละเอียด
            SizedBox(height: 16.0),
            //  ชื่อ
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    widget.product.name,
                    style: TextStyle(
                      fontSize: 24.0,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  // <--- ไอคอนหัวใจ
                  icon: Icon(
                    _isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: _isFavorite
                        ? Colors.red
                        : Colors.grey, // สีหัวใจ (แดง/เทา)
                    size: 30, // ขนาดไอคอน
                  ),
                  onPressed: () {
                    _toggleFavorite();
                  },
                ),
              ],
            ),
            SizedBox(height: 8.0),
            //  ราคา
            Text(
              '\$${widget.product.price.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 22.0,
                color: Colors.blue,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 24.0),
            // Add to Cart Button
            Center(
              child: SizedBox(
                width: double.infinity, // Make button full width
                child: ElevatedButton(
                  onPressed: () {
                    _addToCart();
                  },
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: Colors.blue,
                    padding: EdgeInsets.symmetric(vertical: 12.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0), // Rounded corners
                    ),
                  ),
                  child: Text('Add to Cart', style: TextStyle(fontSize: 18.0)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
