import 'package:flutter/material.dart';
import '../models/product.dart';
import 'product_detail.dart';

class ProductCard extends StatefulWidget {
  final Product product; // รับ object Product เข้ามา

  final ValueChanged<Product>? onFavoriteToggled;

  const ProductCard({Key? key, required this.product, this.onFavoriteToggled})
    : super(key: key);

  @override
  _ProductCardState createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductCard> {
  late bool _isFavorite; // สถานะของหัวใจ

  @override
  void initState() {
    super.initState();
    _isFavorite =
        widget.product.isFavorite; // กำหนดค่า _isFavorite ให้มีค่าเริ่มต้นจากข้อมูลของ Product
  }

  // ทำหน้าที่ตรวจสอบและอัพเดทสถานะของ _isFavorite
  @override
  void didUpdateWidget(covariant ProductCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.product.isFavorite != _isFavorite) {
      setState(() {
        _isFavorite = widget.product.isFavorite;
      });
    }
  }

  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite; // สลับสถานะเมื่อกด
      widget.product.isFavorite = _isFavorite; // อัปเดตสถานะใน object Product
      widget.onFavoriteToggled?.call(widget.product);

      // popup ข้อความ Save and Unsave
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isFavorite
                ? "Save ${widget.product.name}"
                : "Unsave ${widget.product.name}",
          ),
          duration: Duration(milliseconds: 700), // ระยะเวลาแสดงข้อความ 0.7 วิ
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetail(product: widget.product),
          ),
        );

        if (mounted) {
          setState(() {
            _isFavorite = widget.product.isFavorite;
          });
          widget.onFavoriteToggled?.call(widget.product);
        }
      },
      child: Card(
        clipBehavior: Clip.antiAlias,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(0),
          side: BorderSide(color: Colors.grey.shade300, width: 1.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Stack(
                // ใช้ Stack เพื่อวางไอคอนทับบนรูปภาพ
                children: [
                  // load image form url
                  Positioned.fill(
                    child: Image.network(
                      widget.product.imageUrl,
                      fit: BoxFit.cover,
                      loadingBuilder:
                          (
                            BuildContext context,
                            Widget child,
                            ImageChunkEvent? loadingProgress,
                          ) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value:
                                    loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[200],
                          child: Center(
                            child: Icon(Icons.broken_image, color: Colors.grey),
                          ),
                        );
                      },
                    ),
                  ),

                  // วาง IconButton ที่มุมขวาบน
                  Positioned(
                    top: 8.0,
                    right: 8.0,
                    child: IconButton(
                      icon: Icon(
                        _isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: _isFavorite ? Colors.red : Colors.white,
                      ),
                      onPressed: () {
                        _toggleFavorite();
                      },
                    ),
                  ),
                ],
              ),
            ),

            // เติมชื่อ และ ราคา
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.product.name,
                    style: TextStyle(fontWeight: FontWeight.bold),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.0),
                  Text(
                    '\$${widget.product.price.toStringAsFixed(2)}',
                    style: TextStyle(color: Colors.blue[700]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
