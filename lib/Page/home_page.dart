import 'package:flutter/material.dart';
import '../data/product_data.dart';
import '../models/product.dart';
import '../Popup/product_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends State<HomePage> {
  final ProductDataSource _productDataSource = ProductDataSource();
  late List<Product> _allProducts;

  @override
  void initState() {
    super.initState();
    loadAllProduct(); //โหลดสินค้า
  }

  // เมธอด Public ที่ MainWrapper จะเรียกเพื่อบังคับโหลดข้อมูลใหม่
  void loadAllProductsExplicitly() {
    loadAllProduct();
  }

  //mathod โหลดสิ้นค้าทั้งหมด
  void loadAllProduct() {
    if (mounted) {
      setState(() {
        _allProducts = _productDataSource.allProduct;
      });
    }
  }

  // โหลดข้อมูลใหม่ เมื่อข้อมูลมีการเปลี่ยนแปลง
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    loadAllProduct();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // แบ่งการจัดการเป็น 3 ส่วน ดังนี้

      // 1. appBar ส่วนบนของจอ
      appBar: AppBar(
        title: Text('For You', style: TextStyle(fontWeight: FontWeight.bold)),
        centerTitle: false, // ทำให้ข้อความชิดซ้าย
      ),

      // 2. Body ส่วนแสดงผลบริเวณกลางจอ ใช้แสดง Card Product
      body: GridView.count(
        crossAxisCount: 2,
        padding: const EdgeInsets.all(16.0),
        mainAxisSpacing: 16.0, // ระยะห่างแนวตั้งระหว่างการ์ด
        crossAxisSpacing: 16.0, // ระยะห่างแนวนอนระหว่างการ์ด
        childAspectRatio: 0.75, // ปรับอัตราส่วนการ์ด
        children: <Widget>[
          // Function สำหรับการสร้าง Card Product แบบ loop
          ..._allProducts.map((product) {
            return ProductCard(
              key: ValueKey(product.id),
              product: product,
              // อัทเดทสถานะ Favorite เมื่อกด Favorite ที่หน้า Product Detail
              onFavoriteToggled: (updatedProduct) {
                if (mounted) {
                  setState(() {});
                }
              },
            );
          }).toList(),
        ],
      ),
    );
  }
}
