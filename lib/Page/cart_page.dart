import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:provider/provider.dart';
import '../models/product.dart';
import '../data/cart_data_source.dart';
import '../Popup/checkout.dart';

class CartPage extends StatefulWidget {
  const CartPage({Key? key}) : super(key: key);

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CartDataSource>(
      builder: (context, cartDataSource, child) {
        final List<CartItem> cartItems = cartDataSource.cartItems;
        final double totalPrice = cartDataSource.calculateTotalPrice();

        return Scaffold(
          // ด้านบนจอ
          appBar: AppBar(
            title: const Text('Cart'),
            automaticallyImplyLeading: false,
          ),
          // ส่วนกลางจอ แสดงรายการในตะกร้า
          body: Column(
            children: [
              Expanded(
                child: cartItems.isEmpty
                    ? const Center(
                        child: Text(
                          'Your cart is empty!',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        itemCount: cartItems.length,
                        itemBuilder: (context, index) {
                          final CartItem item = cartItems[index];
                          final Product product = item.product;

                          return Slidable(
                            key: ValueKey(
                              item.product.id,
                            ), // ใช้ ID สินค้าเป็น Key
                            endActionPane: ActionPane(
                              motion: const ScrollMotion(),
                              extentRatio: 0.25,
                              children: [
                                SlidableAction(
                                  onPressed: (context) {
                                    // เมื่อกดปุ่มลบ
                                    cartDataSource.removeItemFromCart(
                                      product.id,
                                    );
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          '${product.name} removed from cart.',
                                        ),
                                      ),
                                    );
                                  },
                                  backgroundColor: Colors.red,
                                  foregroundColor: Colors.white,
                                  icon: Icons.delete,
                                  label: 'Delete',
                                ),
                              ],
                            ),

                            child: Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 8.0,
                              ),

                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // รูปสินค้า
                                  Container(
                                    width: 80,
                                    height: 80,
                                    color: Colors.grey[300],
                                    child: Image.network(
                                      product.imageUrl,
                                      fit: BoxFit.cover,
                                    ),
                                  ),

                                  const SizedBox(width: 10),

                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // ชื่อสินค้า
                                        Text(
                                          product.name,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),

                                        // ราคา
                                        Text(
                                          '\$${product.price.toStringAsFixed(2)}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey,
                                          ),
                                        ),

                                        const SizedBox(height: 8),

                                        // ปุ่ม + / - จำนวน
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [

                                            // -
                                            IconButton(
                                              onPressed: () {
                                                cartDataSource
                                                    .updateCartItemQuantity(
                                                      product.id,
                                                      item.quantity - 1,
                                                    );
                                              },
                                              icon: const Icon(Icons.remove),
                                            ),

                                            // ตัวเลข
                                            Text(
                                              item.quantity.toString(),
                                              style: const TextStyle(
                                                fontSize: 16,
                                              ),
                                            ),

                                            // +
                                            IconButton(
                                              onPressed: () {
                                                cartDataSource
                                                    .updateCartItemQuantity(
                                                      product.id,
                                                      item.quantity + 1,
                                                    );
                                              },
                                              icon: const Icon(Icons.add),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
              ),

              // ช่องรวมราคา + ปุ่ม Checkuot
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // รวมราคา
                    Text(
                      'Total : \$${totalPrice.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    // ปุ่ม Checkout
                    ElevatedButton(
                      onPressed: () {

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                CheckoutPage(totalPrice: totalPrice), // ส่งข้อมูล totalPrice ไปที่ CheckoutPage
                          ),
                        );

                        cartDataSource.clearCart(); //ล้างตะกร้า
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Checkout successful! Cart cleared.'),
                          ),
                        );
                      },
                      child: const Text('Checkout'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
