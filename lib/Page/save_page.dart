import 'package:flutter/material.dart';
import '../data/product_data.dart';
import '../models/product.dart';
import '../Popup/product_card.dart';

class SavePage extends StatefulWidget {
  const SavePage({Key? key}) : super(key: key);

  @override
  SavePageState createState() => SavePageState();
}

class SavePageState extends State<SavePage> {
  final ProductDataSource _productDataSource = ProductDataSource();

  // สร้างตัวแปรเพื่อรอรับ Product ที่ save
  List<Product> _favoriteProducts = [];

  // โหลดข้อมูลเมื่อเริ่ม
  @override
  void initState() {
    super.initState();
    // เรียกใช้ Funcsion โหลด Product ที่ save ไว้
    _loadFavorites();
  }

  // เมธอด Public ที่ MainWrapper จะเรียกเพื่อบังคับโหลดข้อมูลใหม่
  void loadFavoritesExplicitly() {
    _loadFavorites();
  }

  // เมธอด โหลดข้อมูลที่มีสถานะ favorite = true
  void _loadFavorites() {
    if (mounted) {
      setState(() {
        _favoriteProducts = _productDataSource.favoriteProducts;
      });
    }
  }

  // โหลดข้อมูลใหม่ เมื่อข้อมูลมีการเปลี่ยนแปลง
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadFavorites();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // ส่วนที่ 1 appBar ด้านบน
      appBar: AppBar(
        title: Text(
          'Saved',
          style: TextStyle(
            fontWeight: FontWeight.bold, // ตัวอักษรหนา
          ),
        ),
        centerTitle: false, // ชิดซ้าย
        elevation: 0, // ไม่มีเงา
      ),

      //  ส่วนที่ 2 body สำหรับแสดง Product card
      body: _favoriteProducts.isEmpty
          ? Center(
              // แสดงข้อความถ้าไม่มีสินค้าที่บันทึกไว้
              child: Text(
                'No saved items yet. Tap the heart to save!',
                style: TextStyle(fontSize: 16, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            )
          : GridView.count(
              crossAxisCount: 2, // 2 คอลัมน์
              padding: const EdgeInsets.all(16.0), // ขอบรอบ
              mainAxisSpacing: 16.0, // ระยะห่างแนวตั้ง
              crossAxisSpacing: 16.0, // ระยะห่างแนวนอน
              childAspectRatio: 0.75, // อัตราส่วนความกว้าง/สูงของแต่ละ Card
              children: <Widget>[
                // วนลูปผ่านรายการสินค้า favorite
                ..._favoriteProducts.map((product) {
                  return ProductCard(
                    product: product,
                    onFavoriteToggled: (updatedProduct) {
                      // เมื่อมีการกดหัวใจใน ProductCard บนหน้านี้
                      // ให้ทำการโหลดรายการ favorite ใหม่ทันที เพื่อให้สินค้าที่ "unsave" หายไป
                      _loadFavorites();
                    },
                  );
                }).toList(),
              ],
            ),
    );
  }
}
