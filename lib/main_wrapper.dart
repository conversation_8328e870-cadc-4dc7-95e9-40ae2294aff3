import 'package:flutter/material.dart';
import 'Page/home_page.dart';
import 'Page/save_page.dart';
import 'Page/cart_page.dart';

class MainWrapper extends StatefulWidget {
  const MainWrapper({Key? key}) : super(key: key);

  @override
  State<MainWrapper> createState() => _MainWrapperState();
}

class _MainWrapperState extends State<MainWrapper> {
  int _selectedIndex = 0; // กำหนดค่าเริ่ม index = 0 (home)

  final GlobalKey<HomePageState> _homePageKey = GlobalKey<HomePageState>();
  final GlobalKey<SavePageState> _savedPageKey = GlobalKey<SavePageState>();

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [  // list ที่ใช้เก็บ Page ทั้งหมด เริ่มจากตำแหน่ง 0 (HOMEPAGE)
      HomePage(key: _homePageKey), // กำหนดให้ _homePageKey คือ GlobalKey ของหน้า HOME เพื่อให้สามารถเข้าไปเรียกใช้งาน mathod ภายในหน้า HOME ได้
      SavePage(key: _savedPageKey),
      CartPage(),
    ];
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index; // index เริ่มต้นคือ 0 ดังนั้นถ้า HOME คือหน้าแรกที่ถูกเรียกใช้งาน
    });

    if (index == 0) {
      _homePageKey.currentState?.loadAllProductsExplicitly();  // ใช้ GlobalKey เพื่อเข้าถึง Mathod loadAllProductsExplicitly ที่อยู่ภายในหน้า HOME
    }
    if (index == 1) {
      _savedPageKey.currentState?.loadFavoritesExplicitly();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _pages),
      bottomNavigationBar: BottomNavigationBar(
        selectedItemColor: Colors.black,
        unselectedItemColor: Colors.grey,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.favorite), label: 'Saved'),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Cart',
          ),
        ],
      ),
    );
  }
}
