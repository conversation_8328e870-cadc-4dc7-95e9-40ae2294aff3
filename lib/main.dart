import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'data/cart_data_source.dart';
import 'data/product_data.dart';
import 'main_wrapper.dart';

void main() {
  ProductDataSource(); // โหลดข้อมูล Productdatasource ก่อนเพื่อให้สามารถเรียกใช้งานได้ทันที
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CartDataSource()),
      ],
      child: MaterialApp(
        title: 'Fluter App',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          appBarTheme: AppBarTheme(
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
          ),
        ),
        home: MainWrapper(), //กำหนดให้ MainWrapper คือหน้าแรกของ App
      ),
    );
  }
}
