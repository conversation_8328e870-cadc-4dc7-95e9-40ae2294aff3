🛒 E-commerce App - Flutter
แอปพลิเคชัน E-commerce ตัวอย่างที่พัฒนาด้วย Flutter โปรเจกต์นี้จัดทำขึ้นเพื่อทดสอบความสามารถก่อนการสัมภาษณ์เข้างาน บริษัท ประชากิจมอเตอร์เชลล์ จำกัด โดยจะแสดงพื้นฐานของแอปพลิเคชันซื้อของออนไลน์ รวมถึงการแสดงรายการสินค้า, การจัดการสินค้าที่บันทึกไว้ (Favorites), ตะกร้าสินค้า, และหน้าชำระเงิน.

แอพพลิเคชั่นนี้ ไม่มีระบบฐานข้อมูล เป็นการแสดงผลโดยบันทึกข้อมูลผ่านตัวแปรในรูปแบบต่างๆ ดังนั้นหากปิดและเปิดแอพพลิเคชั่นใหม่ ข้อมูลจะถูก Reset กลับเป็นค่าเดิมเสมอ!

✨ คุณสมบัติหลัก
หน้าแรก (Home Page): แสดงรายการสินค้าทั้งหมด.

หน้าสินค้าที่บันทึกไว้ (Saved Page): จัดการรายการสินค้าโปรดที่ได้เลือกเอาไว้ โดยการกดหัวใจเพื่อบันทึกหรือยกเลิกการบันทึกสินค้าได้.

หน้าตะกร้าสินค้า (Cart Page):

เพิ่มสินค้าจากหน้ารายละเอียดสินค้า.

แสดงรายการสินค้าในตะกร้าพร้อมราคาและจำนวน.

สามารถเพิ่ม/ลดจำนวนสินค้าได้.

Swipe รายการสินค้าเพื่อเปิดเผยปุ่ม "Delete" สำหรับลบสินค้าออกจากตะกร้า.

หน้ารายละเอียดสินค้า (Product Detail Page): แสดงข้อมูลสินค้าโดยละเอียดและปุ่ม "Add to Cart" รวมถึงปุ่มบันทึก/ยกเลิกการบันทึก.

หน้าชำระเงิน (Checkout Page): แสดงยอดรวมและ QR Code สำหรับการสแกนเพื่อชำระเงิน. (ปัจจุบันเป็นการจำลอง QR Code ที่มีข้อความ "Testing" 
และแอพพลิเคชั่นมีความสามารถในการเชื่อมต่อกับ API ภายนอกได้หากมีลิงค์ API ที่ชัดเจน)

🚀 การเริ่มต้นการใช้งาน (Getting Started)
ดำเนินการตามขั้นตอนเหล่านี้เพื่อตั้งค่าและรันโปรเจกต์.

ข้อกำหนดเบื้องต้น
Flutter SDK (เวอร์ชัน 3.x.x หรือสูงกว่า)
Android Studio หรือ VS Code พร้อม Flutter และ Dart Plugins.

การติดตั้ง
ีืUnzip โฟรเดอร์ 
Open โฟรเดอร์ด้วย VS Code หรือ Android Studio
ติดตั้ง dependencies:
เปิด Bash หรือ command prompt จากนั้น run คำสั่ง
>flutter pub get

การรันแอปพลิเคชัน:
เปิด Bash หรือ command prompt จากนั้น run คำสั่ง
>flutter run

🛠️ เทคโนโลยีที่ใช้
Flutter: Framework สำหรับการพัฒนา Mobile App
Provider: สำหรับการจัดการสถานะ (State Management)
flutter_slidable: สำหรับการสร้าง swipe actions ใน ListView
http: สำหรับการเรียกใช้ HTTP requests (เช่น การดึง QR Code จาก API)
qr_flutter: สำหรับการสร้างและแสดง QR Code

📂 โครงสร้างโปรเจกต์ (Project Structure)
.
├── lib/
│   ├── data/
│   │   ├── cart_data_source.dart      # จัดการข้อมูลตะกร้าสินค้า
│   │   └── product_data.dart          # จัดการข้อมูลสินค้าและสถานะ favorite
│   ├── models/
│   │   └── product.dart               # โมเดลข้อมูลสินค้า
│   ├── Page/
│   │   ├── cart_page.dart             # หน้าตะกร้าสินค้า
│   │   ├── home_page.dart             # หน้าแรกแสดงสินค้า
│   │   └──  save_page.dart             # หน้าสินค้าที่บันทึกไว้ (Saved)
│   ├── Popup/
│   │   ├── checkout.dart              # หน้าชำระเงิน (แสดง QR Code)
│   │   ├── product_card.dart          # Widget สำหรับแสดงสินค้าแต่ละชิ้น
│   │   └── product_detail.dart        # หน้ารายละเอียดสินค้า             
│   ├── main_wrapper.dart              # จัดการ Bottom Navigation Bar
│   └──  main.dart                      # จุดเริ่มต้นของแอปและ Provider setup           
├── pubspec.yaml
└── README.md